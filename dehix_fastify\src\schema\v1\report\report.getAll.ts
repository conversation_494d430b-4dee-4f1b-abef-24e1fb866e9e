import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const getReportsSchema: FastifySchema = {
  description: "API to get all reports",
  tags: ["Reports"],
  querystring: {
    type: "object",
    properties: {
      "filter[report_type]": { type: "string" },
      "filter[status]": { type: "string" },
      "filter[search][value]": { type: "string" },
      "filter[search][columns]": { type: "string" },
      page: { type: "string" },
      limit: { type: "string" },
    },
  },
  response: {
    200: {
      description: "Success",
      type: "object",
      properties: {
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              _id: { type: "string" },
              report_type: { type: "string" },
              report_role: { type: "string" }, // ✅ ADD THIS

              description: { type: "string" },
              subject: { type: "string" },
              reportedById: { type: "string" },
              status: { type: "string" },
              createdAt: {
                type: "string",
                format: "date-time",
              },
              updatedAt: {
                type: "string",
                format: "date-time",
              },

              // ✅ Updated imageMeta to array of objects
              imageMeta: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    Location: { type: "string" },
                    Key: { type: "string" },
                    Bucket: { type: "string" },
                  },
                  required: ["Location", "Key", "Bucket"],
                },
              },
            },
            required: [], // Add required fields if needed
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};
