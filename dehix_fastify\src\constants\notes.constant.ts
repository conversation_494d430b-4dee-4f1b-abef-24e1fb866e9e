// Base endpoint for all notes-related operations
export const NOTES_END_POINT = "/notes";

// Endpoint for creating a new note
export const CREATE_NOTE_END_POINT = "";

// Endpoint for retrieving all notes by user ID
export const GET_NOTE_END_POINT = "";

// Endpoint for retrieving a note by its unique note ID
export const GET_NOTE_BY_ID = "/:note_id";

// Endpoint for deleting a specific note using its note ID
export const DELETE_NOTE_END_POINT = "/:note_id";

// Endpoint to update the note using its note ID
export const UPDATE_NOTE_BY_ID = "/:note_id";

export const UPDATE_NOTE_ORDER = "/update-noteorder";
