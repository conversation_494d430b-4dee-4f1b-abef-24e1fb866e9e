CREATE TABLE `venue` (
  `id` varchar(255) NOT NULL,
  `name` varchar(200) NOT NULL,
  `has_multiple_event_spaces` BOOLEAN DEFAULT NULL,
  `is_business_entity` <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT NULL,
  `venue_entity_type` VARCHAR(255) DEFAULT NULL,
  `traditional_style` JSO<PERSON> DEFAULT NULL,
  `modern_contemporary_style` VARCHAR(255) DEFAULT NULL,
  `eclectic_style` JSON DEFAULT NULL,
  `cultural_style` VARCHAR(255) DEFAULT NULL,
  `venue_hire` VARCHAR(255) DEFAULT NULL,
  `venue_type` VARCHAR(255) DEFAULT NULL,
  `venue_type_on_display` VARCHAR(255) DEFAULT NULL,
  `space_type` VARCHAR(255) DEFAULT NULL,
  `chain` VARCHAR(255) DEFAULT NULL,
  `brand` VARCHAR(255) DEFAULT NULL,
  `description` TEXT DEFAULT NULL,
  `website` VARCHAR(2083) DEFAULT NULL,
  `addr_line1` VARCHAR(255) DEFAULT NULL,
  `addr_line2` VARCHAR(255) DEFAULT NULL,
  `address_id` INT DEFAULT NULL,
  `map_location_url` VARCHAR(2083) DEFAULT NULL,
  `manager_info` JSON DEFAULT NULL,
  `images` JSON DEFAULT NULL,
  `video_link` TEXT DEFAULT NULL,
  `video_upload_type` VARCHAR(255) DEFAULT NULL,
  `total_page_visits` INT DEFAULT NULL,
  `total_time_spent_on_page` INT DEFAULT NULL,
  `accessibility_features` VARCHAR(255) DEFAULT NULL,
  `has_accomodation` BOOLEAN DEFAULT NULL,
  `accomodation_base_room_price` DECIMAL(65, 30) DEFAULT NULL,
  `accommodation_seasonality` JSON DEFAULT NULL,
  `venue_size` VARCHAR(255) DEFAULT NULL,
  `venue_size_unit` VARCHAR(255) DEFAULT NULL,
  `standing_capacity` INT DEFAULT NULL,
  `sitting_capacity` INT DEFAULT NULL,
  `theatre_capacity` INT DEFAULT NULL,
  `dining_capacity` INT DEFAULT NULL,
  `board_room_capacity` INT DEFAULT NULL,
  `u_shaped_capacity` INT DEFAULT NULL,
  `cabaret_capacity` INT DEFAULT NULL,
  `available_menu` JSON DEFAULT NULL,
  `cuisine_type` VARCHAR(255) DEFAULT NULL,
  `is_external_catering_allowed` BOOLEAN DEFAULT NULL,
  `alcohol_liquor_license_time` VARCHAR(255) DEFAULT NULL,
  `is_corkage_fee_aplicable_for_byo_alcohol` BOOLEAN DEFAULT NULL,
  `bring_your_own_alcohol_allowed` BOOLEAN DEFAULT NULL,
  `refreshments_available` VARCHAR(255) DEFAULT NULL,
  `shisha` BOOLEAN DEFAULT NULL,
  `serving_style` VARCHAR(255) DEFAULT NULL,
  `age_limit` INT DEFAULT NULL,
  `age_policy` VARCHAR(255) DEFAULT NULL,
  `is_ticketed_events_allowed` BOOLEAN DEFAULT NULL,
  `max_indoor_music_time` INT DEFAULT NULL,
  `max_outdoor_music_time` INT DEFAULT NULL,
  `Is_own_music_allowed` BOOLEAN DEFAULT NULL,
  `bring_your_own_dj` BOOLEAN DEFAULT NULL,
  `music_speakers` BOOLEAN DEFAULT NULL,
  `are_there_noise_restrictions` BOOLEAN DEFAULT NULL,
  `floor_plan` JSON DEFAULT NULL,
  `trade_license` JSON DEFAULT NULL,
  `sample_menus` JSON DEFAULT NULL,
  `parking_available` BOOLEAN DEFAULT NULL,
  `valet_parkin` BOOLEAN DEFAULT NULL,
  `business_entity_id` varchar(255) DEFAULT NULL,
  `subscription_id` varchar(255) DEFAULT NULL,
  `Created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);