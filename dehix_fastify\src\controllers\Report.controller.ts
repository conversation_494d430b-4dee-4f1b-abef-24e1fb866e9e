import { Controller, POST, GET, Inject, PUT } from "fastify-decorators";
import { FastifyRequest, FastifyReply } from "fastify";
import { ReportService } from "../services/report.service";
import { createReportSchema } from "../schema/v1/report/report.create";
import { getReportsSchema } from "../schema/v1/report/report.getAll";
import { CreateReportBody } from "../types/v1/report/createReport";
import {
  REPORT_ISSUE_ENDPOINT,
  ADMIN_GET_REPORTS_ENDPOINT,
  GET_MESSAGES_BY_ID,
  ADMIN_GET_REPORT_BY_ID_ENDPOINT, // Add this constant, e.g. '/reports/:id'
  ADMIN_UPDATE_REPORT_STATUS_ENDPOINT,
} from "../constants/report.constant";
import {
  STATUS_CODES,
  RESPONSE_MESSAGE,
  ERROR_CODES,
  GetFilterQueryParams,
} from "../common/constants";
import { extractFilters } from "../common/utils";
import { AuthController } from "../common/auth.controller";
import { roleCheck } from "../middlewares/roleCheck.middleware";
@Controller("")
export default class FreelancerReportController extends AuthController {
  @Inject(ReportService)
  reportService!: ReportService;

  @POST(REPORT_ISSUE_ENDPOINT, { schema: createReportSchema })
  async reportIssue(
    request: FastifyRequest<{ Body: CreateReportBody }>,
    reply: FastifyReply,
  ) {
    try {
      const data = await this.reportService.createReport(request.body);
      reply.status(STATUS_CODES.SUCCESS).send({ data });
    } catch (err) {
      reply.status(STATUS_CODES.SERVER_ERROR).send({
        message: RESPONSE_MESSAGE.SERVER_ERROR,
        code: ERROR_CODES.SERVER_ERROR,
      });
    }
  }

  @GET(ADMIN_GET_REPORTS_ENDPOINT, {
    schema: getReportsSchema,
    preHandler: roleCheck(["super admin", "admin"]),
  })
  async getReports(
    request: FastifyRequest<{ Querystring: GetFilterQueryParams }>,
    reply: FastifyReply,
  ) {
    try {
      const { page, limit } = request.query;
      const filters = extractFilters(request.url);

      const data = await this.reportService.getAllReports(filters, page, limit);
      if (!data) {
        return reply.status(STATUS_CODES.NOT_FOUND).send({
          message: RESPONSE_MESSAGE.NOT_FOUND("Reports"),
          code: ERROR_CODES.NOT_FOUND,
        });
      }

      reply.status(STATUS_CODES.SUCCESS).send({ data });
    } catch (error: any) {
      if (
        error.code === ERROR_CODES.NOT_FOUND ||
        error.message.includes("Data not found")
      ) {
        reply.status(STATUS_CODES.NOT_FOUND).send({
          message: RESPONSE_MESSAGE.DATA_NOT_FOUND,
          code: ERROR_CODES.NOT_FOUND,
        });
      } else {
        reply.status(STATUS_CODES.SERVER_ERROR).send({
          message: RESPONSE_MESSAGE.SERVER_ERROR,
          code: ERROR_CODES.SERVER_ERROR,
        });
      }
    }
  }
  @GET("/reports/user/:userId")
  async getReportsByUser(
    request: FastifyRequest<{
      Params: { userId: string };
      Querystring: { page: string; limit: string };
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { userId } = request.params;
      const { page = "1", limit = "10" } = request.query;
      console.log("Fetching reports for userId:", userId); // ✅ add this

      const data = await this.reportService.getReportsByUserId(
        userId,
        page,
        limit,
      );

      if (!data || data.length === 0) {
        return reply.status(404).send({
          message: "Reports for user not found",
          code: "NOT_FOUND",
        });
      }

      reply.send({ data });
    } catch (err) {
      console.error("Failed to fetch user reports:", err);
      reply.status(500).send({
        message: "Server error",
        code: "SERVER_ERROR",
      });
    }
  }

  @GET(ADMIN_GET_REPORT_BY_ID_ENDPOINT)
  async getReportById(
    request: FastifyRequest<{ Params: { id: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { id } = request.params;

      const data = await this.reportService.getReportById(id);

      if (!data) {
        return reply.status(STATUS_CODES.NOT_FOUND).send({
          message: RESPONSE_MESSAGE.NOT_FOUND("Report"),
          code: ERROR_CODES.NOT_FOUND,
        });
      }

      reply.status(STATUS_CODES.SUCCESS).send({ data });
    } catch (err) {
      console.error(`Get report by ID failed:`, err);
      reply.status(STATUS_CODES.SERVER_ERROR).send({
        message: RESPONSE_MESSAGE.SERVER_ERROR,
        code: ERROR_CODES.SERVER_ERROR,
      });
    }
  }

  @PUT(ADMIN_UPDATE_REPORT_STATUS_ENDPOINT)
  async updateReportStatus(
    request: FastifyRequest<{
      Params: { id: string };
      Body: { status: "OPEN" | "CLOSED" };
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { id } = request.params;
      const { status } = request.body;

      const updatedReport = await this.reportService.updateReportStatus(
        id,
        status,
      );

      if (!updatedReport) {
        return reply.status(STATUS_CODES.NOT_FOUND).send({
          message: RESPONSE_MESSAGE.NOT_FOUND("Report"),
          code: ERROR_CODES.NOT_FOUND,
        });
      }

      reply.status(STATUS_CODES.SUCCESS).send({ data: updatedReport });
    } catch (err) {
      console.error("Update report status failed:", err);
      reply.status(STATUS_CODES.SERVER_ERROR).send({
        message: RESPONSE_MESSAGE.SERVER_ERROR,
        code: ERROR_CODES.SERVER_ERROR,
      });
    }
  }

  @POST(GET_MESSAGES_BY_ID)
  async addMessageToReport(
    request: FastifyRequest<{
      Params: { id: string };
      Body: { sender: string; text: string };
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { id } = request.params;
      const { sender, text } = request.body;

      const data = await this.reportService.addMessage(id, sender, text);

      reply.status(STATUS_CODES.SUCCESS).send({ data });
    } catch (error: any) {
      if (
        error.code === ERROR_CODES.NOT_FOUND ||
        error.message.includes("not found")
      ) {
        reply.status(STATUS_CODES.NOT_FOUND).send({
          message: RESPONSE_MESSAGE.NOT_FOUND("Report"),
          code: ERROR_CODES.NOT_FOUND,
        });
      } else {
        reply.status(STATUS_CODES.SERVER_ERROR).send({
          message: RESPONSE_MESSAGE.SERVER_ERROR,
          code: ERROR_CODES.SERVER_ERROR,
        });
      }
    }
  }
}
