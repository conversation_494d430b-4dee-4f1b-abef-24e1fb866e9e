import { Service, Inject } from "fastify-decorators";
import { ReportDAO } from "../dao/report.dao";
import { CreateReportBody } from "../types/v1/report/createReport";
import { NotFoundError } from "../common/errors";
import { ERROR_CODES, RESPONSE_MESSAGE } from "../common/constants";

@Service()
export class ReportService {
  @Inject(ReportDAO)
  private ReportDAO!: ReportDAO;

  // Create a new report
  async createReport(data: CreateReportBody) {
    console.log("📝 Incoming report payload to save:", data);

    try {
      // You may validate report_role here if needed
      if (!data.report_role) {
        throw new Error("report_role is required");
      }

      return await this.ReportDAO.createReport(data);
    } catch (error) {
      console.error("Failed to create report:", error);
      throw error;
    }
  }

  // Get all reports (with filters, pagination)
  async getAllReports(
    filters: Record<string, string[]>,
    page: string,
    limit: string,
  ) {
    try {
      const reports = await this.ReportDAO.getReports(filters, page, limit);

      if (!reports || reports.length === 0) {
        throw new NotFoundError(
          RESPONSE_MESSAGE.NOT_FOUND("Reports"),
          ERROR_CODES.FREELANCER_NOT_FOUND,
        );
      }

      return reports;
    } catch (error) {
      console.error("Failed to fetch reports:", error);
      throw error;
    }
  }

  // Add a message to a report
  async addMessage(reportId: string, sender: string, text: string) {
    try {
      if (!text.trim()) {
        throw new Error("Message text cannot be empty");
      }

      const report = await this.ReportDAO.addMessageToReport(
        reportId,
        sender,
        text,
      );

      if (!report) {
        throw new NotFoundError(
          RESPONSE_MESSAGE.NOT_FOUND("Report"),
          ERROR_CODES.FREELANCER_NOT_FOUND,
        );
      }

      return report;
    } catch (error) {
      console.error(`Failed to add message to report ${reportId}:`, error);
      throw error;
    }
  }
  // report.service.ts
  async getReportsByUserId(userId: string, page: string, limit: string) {
    const reports = await this.ReportDAO.getReportsByUserId(
      userId,
      page,
      limit,
    );

    if (!reports || reports.length === 0) {
      throw new NotFoundError(
        RESPONSE_MESSAGE.NOT_FOUND("Reports for user"),
        ERROR_CODES.NOT_FOUND,
      );
    }

    return reports;
  }

  // Get report by ID
  async getReportById(reportId: string) {
    try {
      const report = await this.ReportDAO.findReportById(reportId);

      if (!report) {
        throw new NotFoundError(
          RESPONSE_MESSAGE.NOT_FOUND("Report"),
          ERROR_CODES.FREELANCER_NOT_FOUND,
        );
      }

      return report;
    } catch (error) {
      console.error(`Failed to fetch report ${reportId}:`, error);
      throw error;
    }
  }

  async updateReportStatus(id: string, status: "OPEN" | "CLOSED") {
    const updatedReport = await this.ReportDAO.updateStatusById(id, status);
    return updatedReport;
  }
}
