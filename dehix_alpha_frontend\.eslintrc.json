{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "import", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:@next/next/recommended", "plugin:prettier/recommended"], "rules": {"import/no-unresolved": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/explicit-module-boundary-types": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always"}], "jsx-a11y/no-onchange": "off", "@typescript-eslint/no-explicit-any": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/no-noninteractive-element-interactions": "off", "prettier/prettier": "error", "jsx-a11y/media-has-caption": "off"}, "settings": {"react": {"version": "detect"}}}