import { Service } from "fastify-decorators";
import { v4 as uuidv4 } from "uuid";
import { ReportModel, IReport } from "../models/report.entity";
import { fetchDataWithQueries } from "../common/utils";

@Service()
export class ReportDAO {
  // Create a new report
  async createReport(data: Partial<IReport>) {
    console.log(data);
    try {
      const report = await ReportModel.create({
        _id: uuidv4(),
        ...data,
        status: data.status || "OPEN", // allow override if provided
        createdAt: new Date(),
      });
      console.log(report);
      return report;
    } catch (error: any) {
      throw new Error(`Failed to create report: ${error.message}`);
    }
  }

  // Get reports with filters + pagination
  async getReports(
    filters: Record<string, any>,
    page: string = "1",
    limit: string = "10",
  ) {
    console.log("Filters passed to DAO:", filters); // ✅ add this

    try {
      return await fetchDataWithQueries(ReportModel, filters, page, limit);
    } catch (error: any) {
      throw new Error(`Failed to fetch reports: ${error.message}`);
    }
  }
  async getReportsByUserId(
    userId: string,
    page: string = "1",
    limit: string = "10",
  ) {
    try {
      const pageIndex = (parseInt(page) - 1) * parseInt(limit);
      const query = { reportedById: userId };

      const data = await ReportModel.find(query)
        .sort({ createdAt: -1 })
        .skip(pageIndex)
        .limit(parseInt(limit));

      return data;
    } catch (error: any) {
      throw new Error(`Failed to fetch user reports: ${error.message}`);
    }
  }

  // Find a single report by ID
  async findReportById(reportId: string) {
    try {
      return await ReportModel.findById(reportId);
    } catch (error: any) {
      throw new Error(`Failed to find report by ID: ${error.message}`);
    }
  }

  // Update a report by ID
  async updateReport(reportId: string, update: Partial<IReport>) {
    try {
      return await ReportModel.findByIdAndUpdate(
        reportId,
        { ...update, updatedAt: new Date() },
        { new: true },
      );
    } catch (error: any) {
      throw new Error(`Failed to update report: ${error.message}`);
    }
  }

  // Add a message to a report
  async addMessageToReport(reportId: string, sender: string, text: string) {
    try {
      const message = {
        id: uuidv4(),
        sender,
        text,
        timestamp: new Date(),
      };

      const report = await ReportModel.findByIdAndUpdate(
        reportId,
        { $push: { messages: message }, updatedAt: new Date() },
        { new: true },
      );

      if (!report) {
        throw new Error("Report not found");
      }

      return report;
    } catch (error: any) {
      throw new Error(`Failed to add message: ${error.message}`);
    }
  }
  async updateStatusById(id: string, status: "OPEN" | "CLOSED") {
    const updated = await ReportModel.findByIdAndUpdate(
      id,
      { status },
      { new: true }, // Return the updated document
    );
    return updated;
  }

  // Delete a report by ID
  async deleteReport(reportId: string) {
    try {
      return await ReportModel.findByIdAndDelete(reportId);
    } catch (error: any) {
      throw new Error(`Failed to delete report: ${error.message}`);
    }
  }

  // Count total reports
  async countReports(): Promise<number> {
    try {
      return await ReportModel.countDocuments();
    } catch (error: any) {
      throw new Error(`Failed to count reports: ${error.message}`);
    }
  }
}
