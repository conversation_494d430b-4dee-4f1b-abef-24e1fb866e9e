// created by <PERSON><PERSON><PERSON>
// date: 24/9/2024

export const Dehix_Talent_Card_Pagination = {
  BATCH: 3,
};

// Enum for Notification Type
export enum UserNotificationTypeEnum {
  PROJECT_HIRING = 'PROJECT_HIRING',
  SKILL_INTERVIEW = 'SKILL_INTERVIEW',
  DOMAIN_INTERVIEW = 'DOMAIN_INTERVIEW',
  TALENT_INTERVIEW = 'TALENT_INTERVIEW',
}

export interface UserNotification {
  id: string;
  message: string;
  type: UserNotificationTypeEnum;
  entity: string;
  path: string;
  userId: string;
}

export enum HireDehixTalentStatusEnum {
  ADDED = 'Added',
  APPROVED = 'Approved',
  CLOSED = 'Closed',
  COMPLETED = 'Completed',
}

export enum BusinessStatusEnum {
  ACTIVE = 'Active',
  IN_ACTIVE = 'Inactive',
  NOT_VERIFIED = 'Not Verified',
}

export enum NotificationTypeEnum {
  BUSINESS = 'Business',
  FREELANCER = 'Freelancer',
  BOTH = 'Both',
}
export enum NotificationStatusEnum {
  ACTIVE = 'Active',
  IN_ACTIVE = 'Inactive',
}

// Enum for Oracle Status
export enum OracleStatusEnum {
  APPLIED = 'APPLIED',
  NOT_APPLIED = 'NOT_APPLIED',
  APPROVED = 'APPROVED',
  FAILED = 'FAILED',
  STOPPED = 'STOPPED',
  REAPPLIED = 'REAPPLIED',
}
// Enum for Bid Status
export enum BidstatusEnum {
  PENDING = 'Pending',
  ACCEPTED = 'Accepted',
  REJECTED = 'Rejected',
  PANEL = 'Panel',
  INTERVIEW = 'Interview',
}

export enum DomainStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export enum FreelancerStatusEnum {
  ACTIVE = 'Active',
  PENDING = 'Pending',
  INACTIVE = 'Inactive',
  CLOSED = 'Closed',
}

export enum Type {
  FREELANCER = 'FREELANCER',
  ADMIN = 'ADMIN',
  BUSINESS = 'BUSINESS',
}

export enum TicketStatus {
  CREATED = 'Created',
  CLOSED = 'Closed',
  ACTIVE = 'Active',
}
