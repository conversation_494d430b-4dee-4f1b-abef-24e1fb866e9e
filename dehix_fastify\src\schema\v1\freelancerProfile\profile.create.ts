import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const createFreelancerProfileSchema: FastifySchema = {
  description: "API to create a new freelancer profile",
  tags: ["Freelancer Profile"],
  body: {
    type: "object",
    required: ["profileName", "description"],
    properties: {
      profileName: {
        type: "string",
        minLength: 1,
        maxLength: 100,
        description:
          "Name of the profile (e.g., Frontend Developer, Backend Engineer)",
      },
      description: {
        type: "string",
        minLength: 10,
        maxLength: 500,
        description: "Detailed description of the profile and expertise",
      },
      skills: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of skill IDs to include in this profile",
      },
      domains: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of domain IDs to include in this profile",
      },
      projects: {
        type: "array",
        items: {
          type: "object",
          properties: {
            _id: { type: "string" },
            projectName: { type: "string" },
            description: { type: "string" },
            role: { type: "string" },
            start: { type: "string", format: "date-time" },
            end: { type: "string", format: "date-time" },
            techUsed: {
              type: "array",
              items: { type: "string" },
            },
            githubLink: { type: "string" },
            liveDemoLink: { type: "string" },
            thumbnail: { type: "string" },
            projectType: { type: "string" },
            verified: { type: "boolean" },
          },
          required: [
            "_id",
            "projectName",
            "description",
            "role",
            "start",
            "end",
          ],
        },
        description: "Array of project objects to include in this profile",
      },
      experiences: {
        type: "array",
        items: {
          type: "object",
          properties: {
            _id: { type: "string" },
            company: { type: "string" },
            jobTitle: { type: "string" },
            workDescription: { type: "string" },
            workFrom: { type: "string" },
            workTo: { type: "string" },
            referencePersonName: { type: "string" },
            referencePersonContact: { type: "string" },
            githubRepoLink: { type: "string" },
          },
          required: [
            "_id",
            "company",
            "jobTitle",
            "workDescription",
            "workFrom",
            "workTo",
          ],
        },
        description: "Array of experience objects to include in this profile",
      },
      education: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of education IDs to include in this profile",
      },
      portfolioLinks: {
        type: "array",
        items: {
          type: "string",
          format: "uri",
        },
        description: "Array of portfolio links",
      },
      githubLink: {
        type: "string",
        format: "uri",
        description: "GitHub profile URL",
      },
      linkedinLink: {
        type: "string",
        format: "uri",
        description: "LinkedIn profile URL",
      },
      personalWebsite: {
        type: "string",
        format: "uri",
        description: "Personal website URL",
      },
      hourlyRate: {
        type: "number",
        minimum: 0,
        description: "Preferred hourly rate in USD",
      },
      availability: {
        type: "string",
        enum: ["FULL_TIME", "PART_TIME", "CONTRACT", "FREELANCE"],
        description: "Availability type",
      },
    },
    additionalProperties: false,
  },
  response: {
    201: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            _id: { type: "string" },
            freelancerId: { type: "string" },
            profileName: { type: "string" },
            description: { type: "string" },
            skills: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            domains: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            projects: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  projectName: { type: "string" },
                  description: { type: "string" },
                  role: { type: "string" },
                  start: { type: "string" },
                  end: { type: "string" },
                  techUsed: {
                    type: "array",
                    items: { type: "string" },
                  },
                  githubLink: { type: "string" },
                  projectType: { type: "string" },
                  verified: { type: "boolean" },
                },
              },
            },
            portfolioLinks: {
              type: "array",
              items: { type: "string" },
            },
            githubLink: { type: "string" },
            linkedinLink: { type: "string" },
            personalWebsite: { type: "string" },
            hourlyRate: { type: "number" },
            availability: { type: "string" },
            isActive: { type: "boolean" },
            createdAt: { type: "string" },
            updatedAt: { type: "string" },
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};
