// Base endpoint for all domain-related operations
export const DOMAIN_ENDPOINT = "/domain";

// Endpoint for retrieving all domains in the system
export const DOMAIN_ALL_ENDPOINT = "";

// Endpoint to delete a specific domain by its unique domain ID
export const DOMAIN_DELETE_BY_ID_ENDPOINT = "/:domain_id";

// Endpoint for creating a new domain
export const DOMAIN_ID_ENDPOINT = "";

export const GET_ALL_ADMIN_ENDPOINT = "/admin";

// Endpoint to retrieve a specific domain's details by its unique domain ID
export const DOMAIN_BY_ID_ENDPOINT = "/:domain_id";

// Endpoint to update the status of a specific domain using its domain ID
export const DOMAIN_UPDATE_STATUS_BY_ID = "/:domain_id/status";
