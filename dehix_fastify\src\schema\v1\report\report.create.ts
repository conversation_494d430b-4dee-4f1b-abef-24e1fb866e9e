export const createReportSchema = {
  body: {
    type: "object",
    required: [
      "subject",
      "description",
      "report_type",
      "reportedById",
      "report_role",
      "status",
    ],
    properties: {
      subject: { type: "string" },
      description: { type: "string" },
      report_type: {
        type: "string",
        // Optionally add enum values here if needed
      },
      reportedById: { type: "string" },
      report_role: {
        type: "string",
        enum: [
          "user",
          "admin",
          "business",
          "freelancer",
          "User",
          "Admin",
          "Business",
          "Freelancer",
        ],
      },
      status: {
        type: "string",
        enum: ["OPEN", "IN_PROGRESS", "RESOLVED", "CLOSED"],
      },

      // ✅ Updated imageMeta to allow multiple images
      imageMeta: {
        type: "array",
        maxItems: 3,
        items: {
          type: "object",
          required: ["Location", "Key", "Bucket"],
          properties: {
            Location: { type: "string" },
            Key: { type: "string" },
            Bucket: { type: "string" },
          },
        },
      },
    },
  },
};
