{"name": "dehix", "version": "1.0.0", "description": "Backend Service - APIs for Dehix", "main": "index.js", "type": "module", "target": "es2020", "scripts": {"clean": "rm -rf dist", "start": "cross-env NODE_ENV=dev node --loader ts-node/esm src/app.ts ", "dev": "nodemon --watch src --ext ts,js --exec \"cross-env NODE_ENV=dev node --loader ts-node/esm src/app.ts\"", "build": "npm run clean && tsc -p tsconfig.json", "build-dev": "npm run clean && NODE_ENV=dev tsc -p tsconfig.json", "deploy": "npm run build && sls deploy -s prod", "deploy-dev": "npm run build && sls deploy -s dev", "check-format": "prettier --check .", "format": "prettier --write .", "lint": "npx eslint . --ext .js,.jsx,.ts,.tsx", "lint-fix": "npx  eslint --fix  . --ext .js,.jsx,.ts,.tsx", "test": "echo \"Error: no test specified\" && exit 1", "check-types": "tsc --pretty --noEmit", "test-all": "npm run check-format && npm run lint && npm run check-types && npm run build"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.658.0", "@aws-sdk/client-ses": "^3.565.0", "@fastify/aws-lambda": "^3.2.0", "@fastify/cors": "^8.3.0", "@fastify/env": "^4.2.0", "@fastify/multipart": "^8.3.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^3.0.0", "dayjs": "^1.11.13", "fastify": "^4.28.1", "fastify-decorators": "^3.15.0", "fastify-multipart": "^5.4.0", "firebase-admin": "^12.1.0", "googleapis": "^144.0.0", "memory-cache": "^0.2.0", "moment": "^2.30.1", "mongoose": "^8.4.3", "mysql2": "^3.3.3", "node-cron": "^3.0.3", "pdf-lib": "^1.17.1", "pino": "^9.0.0", "pino-pretty": "^11.3.0", "pump": "^3.0.2", "sequelize": "^6.37.3", "stripe": "^15.5.0", "ts-node": "^10.9.1", "uuid": "^9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/mongoose": "^5.11.97", "@types/node": "^20.14.6", "@types/sequelize": "^4.28.15", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^8.0.3", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "prettier": "^3.3.2", "typescript": "5.4.x", "typescript-eslint": "^7.5.0"}, "engines": {"node": ">=18.0.0"}}