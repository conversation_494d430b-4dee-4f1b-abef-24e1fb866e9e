import mongoose, { Schema, Document, Model } from "mongoose";
import { v4 as uuidv4 } from "uuid";

// ImageMeta interface
interface IImageMeta {
  Location: string;
  Key: string;
  Bucket: string;
}

// Message subdocument interface
export interface IMessage {
  id: string;
  sender: string; // "user" or "admin"
  text: string;
  timestamp: Date;
}

// Main Report interface
export interface IReport extends Document {
  _id: string;
  report_type: string;
  report_role: string;
  description: string;
  subject: string;
  reportedById: string;
  status: string;
  messages: IMessage[];
  imageMeta?: IImageMeta[]; // ✅ Updated to array
  createdAt?: Date;
  updatedAt?: Date;
}

// Message sub-schema
const MessageSchema: Schema<IMessage> = new Schema(
  {
    id: {
      type: String,
      default: uuidv4,
    },
    sender: {
      type: String,
      enum: ["user", "admin"],
      required: true,
    },
    text: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  { _id: false },
);

// ImageMeta sub-schema
const ImageMetaSchema: Schema<IImageMeta> = new Schema(
  {
    Location: { type: String, required: true },
    Key: { type: String, required: true },
    Bucket: { type: String, required: true },
  },
  { _id: false },
);

// Main report schema
const ReportSchema: Schema<IReport> = new Schema(
  {
    _id: {
      type: String,
      default: uuidv4,
    },
    report_type: {
      type: String,
      required: true,
    },
    report_role: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    subject: {
      type: String,
      required: true,
    },
    reportedById: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
    },
    messages: {
      type: [MessageSchema],
      default: [],
    },

    // ✅ Updated to support multiple images
    imageMeta: {
      type: [ImageMetaSchema],
      required: false,
      default: [],
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

export const ReportModel: Model<IReport> = mongoose.model<IReport>(
  "Report",
  ReportSchema,
);

export default {
  ReportModel,
};
