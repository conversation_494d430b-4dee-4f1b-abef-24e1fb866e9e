{
  "root": true,
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": "./tsconfig.json",
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "plugins": ["@typescript-eslint", "prettier", "unused-imports"],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "prettier/prettier": "error",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_|^Raw|^Logger", // Ignore variables prefixed with "_", "Raw", or "Logger"
        "varsIgnorePattern": "^_|^Raw|^Logger" // Ignore unused variables prefixed with "_", "Raw", or "Logger"
      }
    ],
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "off",

    // Enable unused-imports plugin rules
    "unused-imports/no-unused-imports": "error", // Automatically detect and remove unused imports
    "unused-imports/no-unused-vars": [
      "warn",
      {
        "vars": "all",
        "varsIgnorePattern": "^_|^Raw|^Logger", // Ignore unused variables prefixed with "_", "Raw", or "Logger"
        "args": "after-used",
        "argsIgnorePattern": "^_|^Raw|^Logger" // Ignore unused function arguments prefixed with "_", "Raw", or "Logger"
      }
    ]
  }
}
