import { FastifyReply, FastifyRequest } from "fastify";
import { STATUS_CODES } from "../common/constants";
export function roleCheck(allowedRoles: string[]) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const userRole = request.decodedToken?.type;

    if (!userRole || !allowedRoles.includes(userRole)) {
      return reply
        .code(STATUS_CODES.FORBIDDEN)
        .send({ message: "Forbidden: Insufficient permissions" });
    }
  };
}
