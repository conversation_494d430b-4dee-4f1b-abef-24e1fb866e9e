import { Document, Schema, model } from "mongoose";
import { v4 as uuidv4 } from "uuid";

export enum AvailabilityEnum {
  FULL_TIME = "FULL_TIME",
  PART_TIME = "PART_TIME",
  CONTRACT = "CONTRACT",
  FREELANCE = "FREELANCE",
}

export interface IFreelancerProfile extends Document {
  _id?: string;
  freelancerId: string;
  profileName: string;
  description: string;
  skills: string[];
  domains: string[];
  projects: {
    _id: string;
    projectName: string;
    description: string;
    role: string;
    start: Date;
    end: Date;
    techUsed: string[];
    githubLink?: string;
    liveDemoLink?: string;
    thumbnail?: string;
    projectType?: string;
    verified?: boolean;
  }[];
  experiences: {
    _id: string;
    company: string;
    jobTitle: string;
    workDescription: string;
    workFrom: string;
    workTo: string;
    referencePersonName?: string;
    referencePersonContact?: string;
    githubRepoLink?: string;
  }[];
  education: string[];
  portfolioLinks?: string[];
  githubLink?: string;
  linkedinLink?: string;
  personalWebsite?: string;
  hourlyRate?: number;
  availability?: AvailabilityEnum;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const FreelancerProfileSchema: Schema = new Schema(
  {
    _id: {
      type: String,
      default: uuidv4,
      required: true,
    },
    freelancerId: {
      type: String,
      ref: "Freelancer",
      required: true,
    },
    profileName: {
      type: String,
      required: true,
      maxlength: 100,
    },
    description: {
      type: String,
      required: true,
      maxlength: 500,
    },
    skills: [
      {
        type: String,
        ref: "Skill",
        required: false,
      },
    ],
    domains: [
      {
        type: String,
        ref: "Domain",
        required: false,
      },
    ],
    projects: [
      {
        _id: { type: String, required: true },
        projectName: { type: String, required: true },
        description: { type: String, required: true },
        role: { type: String, required: true },
        start: { type: Date, required: true },
        end: { type: Date, required: true },
        techUsed: [{ type: String }],
        githubLink: { type: String },
        liveDemoLink: { type: String },
        thumbnail: { type: String },
        projectType: { type: String },
        verified: { type: Boolean, default: false },
      },
    ],
    experiences: [
      {
        _id: { type: String, required: true },
        company: { type: String, required: true },
        jobTitle: { type: String, required: true },
        workDescription: { type: String, required: true },
        workFrom: { type: String, required: true },
        workTo: { type: String, required: true },
        referencePersonName: { type: String },
        referencePersonContact: { type: String },
        githubRepoLink: { type: String },
      },
    ],
    education: [
      {
        type: String,
        required: false,
      },
    ],
    portfolioLinks: [
      {
        type: String,
        required: false,
      },
    ],
    githubLink: {
      type: String,
      required: false,
    },
    linkedinLink: {
      type: String,
      required: false,
    },
    personalWebsite: {
      type: String,
      required: false,
    },
    hourlyRate: {
      type: Number,
      required: false,
      min: 0,
    },
    availability: {
      type: String,
      enum: Object.values(AvailabilityEnum),
      default: AvailabilityEnum.FREELANCE,
      required: false,
    },
    isActive: {
      type: Boolean,
      default: true,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes for better query performance
FreelancerProfileSchema.index({ freelancerId: 1 });
FreelancerProfileSchema.index({ freelancerId: 1, isActive: 1 });
FreelancerProfileSchema.index({ freelancerId: 1, profileName: 1 });

// Ensure profile names are unique per freelancer
FreelancerProfileSchema.index(
  { freelancerId: 1, profileName: 1 },
  { unique: true },
);

const FreelancerProfile = model<IFreelancerProfile>(
  "FreelancerProfile",
  FreelancerProfileSchema,
);
export default FreelancerProfile;
